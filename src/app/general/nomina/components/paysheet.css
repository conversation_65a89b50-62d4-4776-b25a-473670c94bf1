/* Animaciones específicas para collapsibles del paysheet */
@keyframes paysheet-collapsible-down {
  from {
    height: 0;
    opacity: 0;
  }
  to {
    height: var(--radix-collapsible-content-height);
    opacity: 1;
  }
}

@keyframes paysheet-collapsible-up {
  from {
    height: var(--radix-collapsible-content-height);
    opacity: 1;
  }
  to {
    height: 0;
    opacity: 0;
  }
}

.paysheet-collapsible-down {
  animation: paysheet-collapsible-down 0.3s ease-out;
}

.paysheet-collapsible-up {
  animation: paysheet-collapsible-up 0.3s ease-out;
}

/* Ocultar scrollbar solo en el paysheet */
.paysheet-hide-scrollbar {
  -ms-overflow-style: none; /* Internet Explorer 10+ */
  scrollbar-width: none; /* Firefox */
}

.paysheet-hide-scrollbar::-webkit-scrollbar {
  display: none; /* Safari and Chrome */
}

/* Estilos para inputs seamless del paysheet */
.paysheet-seamless-input {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
  padding: 0 !important;
  margin: 0 !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  color: inherit !important;
  line-height: inherit !important;
}

.paysheet-seamless-input:focus {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  ring: none !important;
}

/* Ocultar flechas de number inputs solo en paysheet */
.paysheet-seamless-input[type="number"]::-webkit-outer-spin-button,
.paysheet-seamless-input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.paysheet-seamless-input[type="number"] {
  -moz-appearance: textfield;
}
